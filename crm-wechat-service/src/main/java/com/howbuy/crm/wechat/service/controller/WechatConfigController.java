package com.howbuy.crm.wechat.service.controller;

import com.howbuy.crm.wechat.client.base.ReturnMessageDto;
import com.howbuy.crm.wechat.service.service.config.CacheBaseConfigServce;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 缓存刷新控制器
 * @classname: WechatConfigController
 * @author: haoran.zhang
 * @creatdate: 2025年8月5日 下午5:05:05
 * @since: JDK1.8
 */
@Slf4j
@RestController
@RequestMapping("/wechatconfig")
public class WechatConfigController {

    @Autowired
    private CacheBaseConfigServce cacheBaseConfigServce;


    /**
     * @api {POST} /wechatconfig/reloadcacheconfig reloadCacheConfig()
     * @apiVersion 1.0.0
     * @apiGroup WechatConfigController
     * @apiName reloadCacheConfig()
     * @apiDescription 刷新企微配置信息缓存
     * @apiParam (请求体) {String} companyNo companyNo	企业编码      {@link com.howbuy.crm.wechat.client.enums.String}
     * @apiParamExample 请求体示例
     * {"companyNo":"5HyTT"}
     * @apiSuccess (响应结果) {String} returnCode 返回代码
     * @apiSuccess (响应结果) {String} returnMsg 返回信息体
     * @apiSuccess (响应结果) {String} returnObject 自定义返回对象
     * @apiSuccess (响应结果) {Array} returnList 返回列表
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"A","returnMsg":"YsUY0NI","returnObject":"Fxa","returnList":["aFtGl5"]}
     */
    @GetMapping("/reloadcacheconfig")
    @ResponseBody
    public ReturnMessageDto<String> reloadCacheConfig(String companyNo) {
        try {
            if(StringUtils.isEmpty(companyNo)){
               cacheBaseConfigServce.reloadCorpConfig();
               cacheBaseConfigServce.reloadAppConfig();
            }else{
                cacheBaseConfigServce.reloadCorpConfigByConpanyNo(companyNo);
                cacheBaseConfigServce.reloadAppConfigByCompanyNo(companyNo);
            }
            return ReturnMessageDto.ok();
        } catch (Exception e) {
            log.error("reloadConfig error", e);
            return ReturnMessageDto.fail("系统异常");
        }
    }

}