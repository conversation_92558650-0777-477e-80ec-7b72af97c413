package com.howbuy.crm.wechat.service.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.wechat.client.enums.OutSysTypeEnum;
import com.howbuy.crm.wechat.service.domain.message.HkAcctBindMessageDTO;
import com.howbuy.crm.wechat.service.outerservice.hkacccenter.QueryHkWechatAcctBindOuterService;
import com.howbuy.crm.wechat.service.repository.CmWechatCustInfoRepository;
import com.howbuy.message.MessageService;
import com.howbuy.message.SimpleMessage;
import com.howbuy.message.processor.MessageProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/**
 * @description: 香港外部账号绑定消息
 * @author: jianyi.tao
 * @create: 2022/09/05 10:35
 * @since: JDK 1.7
 */
@Slf4j
@Service
public class MqOuterHkAcctListenerMessage extends MessageProcessor implements InitializingBean {
    @Autowired
    private CmWechatCustInfoRepository cmWechatCustInfoRepository;
    @Autowired
    private QueryHkWechatAcctBindOuterService queryHkWechatAcctBindOuterService;

    @Value("${sync.TOPIC_HK_OUTER_ACCT}")
    private String syncDepositQueue;

    @Override
    public void afterPropertiesSet() {
        MessageService.getInstance().addMessageProcessor(syncDepositQueue, this);
    }

    @Override
    public void processMessage(SimpleMessage simpleMessage) {
        try {
            Object content = simpleMessage.getContent();
            log.info("processMessage,crm-wechat接收mq进行客户信息更新：{}", content);
            JSONObject jsonObject = JSONObject.parseObject((String) content);
            HkAcctBindMessageDTO hkAcctBindMessageDTO = JSON.parseObject(jsonObject.getString("body"), HkAcctBindMessageDTO.class);
            onMessageByte(hkAcctBindMessageDTO);
        } catch (Exception e) {
            log.error("processMessage,crm-wechat接收mq进行客户信息更新error：{}", Throwables.getStackTraceAsString(e));
        }
    }

    public void onMessageByte(HkAcctBindMessageDTO hkAcctBindMessageDTO) {
        try {
            // 三方账号、香港客户号、三方系统类型、绑定状态不能为空
            if (StringUtils.isEmpty(hkAcctBindMessageDTO.getOuterAcct()) || StringUtils.isEmpty(hkAcctBindMessageDTO.getHkCustNo())
                    || StringUtils.isEmpty(hkAcctBindMessageDTO.getOuterSysType()) || StringUtils.isEmpty(hkAcctBindMessageDTO.getBindStatus())) {
                log.error("onMessageByte-三方账号、香港客户号、三方系统类型、绑定状态不能为空,hkAcctBindMessageDTO:{}", JSON.toJSONString(hkAcctBindMessageDTO));
                return;
            }
            // 只关心微信绑定的状态,其他班类型的绑定直接返回
            if (!OutSysTypeEnum.WECHAT.getType().equals(hkAcctBindMessageDTO.getOuterSysType())) {
                log.info("onMessageByte-只关心微信绑定的状态,其他班类型的绑定直接返回,hkAcctBindMessageDTO:{}", JSON.toJSONString(hkAcctBindMessageDTO));
                return;
            }

            // 绑定外部账号
            if (YesOrNoEnum.NO.getCode().equals(hkAcctBindMessageDTO.getBindStatus())) {
                int num = updateHkHbOneNoBindByUnionId(hkAcctBindMessageDTO.getHkCustNo(), hkAcctBindMessageDTO.getOuterAcct());
                log.info("onMessageByte-更新unionid = {},香港客户号:{}成功条数:{}", hkAcctBindMessageDTO.getOuterAcct(), hkAcctBindMessageDTO.getHkCustNo(), num);
            }

            // 解绑外部账号
            if (YesOrNoEnum.YES.getCode().equals(hkAcctBindMessageDTO.getBindStatus())) {
                int num2 = updateHkHbOneNoBindByUnionId("", hkAcctBindMessageDTO.getOuterAcct());
                log.info("onMessageByte-解除unionid={}的一帐通号成功条数：{}", hkAcctBindMessageDTO.getOuterAcct(), num2);
            }
        } catch (Exception e) {
            log.error("onMessageByte-接收账户中心mq进行一帐通更新失败:{}", Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 更新香港一帐通绑定的外部账号
     *
     * @param hkCustNo
     * @param outerAcct
     * @return
     */
    private int updateHkHbOneNoBindByUnionId(String hkCustNo, String outerAcct) {
        // 历史逻辑：此处只更新 好买财富。
        //入口 companyNo 默认赋值：
        String companyNoEnum=Constants.DEFAULT_COMPANY_NO;

        String hkHboneNo = "";
        if (StringUtils.isNotBlank(hkCustNo)) {
            hkHboneNo = queryHkWechatAcctBindOuterService.queryHboneNoByHkCustNo(hkCustNo);
            if (StringUtils.isBlank(hkHboneNo)) {
                log.info("companyNo:{}, updateHkHbOneNoBindByUnionId-根据香港客户号查询不到一账通号,hkCustNo:{}", companyNoEnum,hkCustNo);
                return 0;
            }
        }

        logger.info("companyNo:{}, updateHkHbOneNoBindByUnionId-更新香港一账通跟unionId绑定关系,hkHboneNo:{},outerAcct:{}",
                companyNoEnum,hkHboneNo, outerAcct);
        String companyNo = companyNoEnum.getCode();
        return cmWechatCustInfoRepository.updateHkHbOneNoByUnionId(companyNo,hkHboneNo, outerAcct);
    }

}