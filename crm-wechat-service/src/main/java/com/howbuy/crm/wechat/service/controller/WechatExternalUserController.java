/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.controller;

import com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserInfoDTO;
import com.howbuy.crm.wechat.service.service.WechatExternalUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 企微外部联系人接口类
 * <AUTHOR>
 * @date 2023/11/1 13:20
 * @since JDK 1.8
 */

@Slf4j
@RestController
@RequestMapping("/wechatexternaluser")
public class WechatExternalUserController {

    @Autowired
    private WechatExternalUserService wechatExternalUserService;

    /**
     * @api {GET} /wechatexternaluser/getexternaluser getExternalUser()
     * @apiVersion 1.0.0
     * @apiGroup WechatExternalUserController
     * @apiName getExternalUser()
     * @apiParam (请求参数) {String} externalUserId
     * @apiParam (请求参数) {String} companyNo
     * @apiParamExample 请求参数示例
     * companyNo=A&externalUserId=J2c
     * @apiSuccess (响应结果) {Array} externalUserProfileList 外部联系人的自定义展示信息，      可以有多个字段和多种类型，包括文本，网页和小程序，      仅当联系人类型是企业微信用户时有此字段，字段详情见对外属性；
     * @apiSuccess (响应结果) {String} externalUserProfileList.type profile自定义类型
     * @apiSuccess (响应结果) {String} externalUserProfileList.name profile自定义名称
     * @apiSuccess (响应结果) {Object} externalUserProfileList.profileMap profile的详细属性
     * @apiSuccess (响应结果) {Array} followUserList 外部客户 vs 企业微信成员  绑定关系列表
     * @apiSuccess (响应结果) {String} followUserList.userid 添加了此外部联系人的企业成员userid
     * @apiSuccess (响应结果) {String} followUserList.externalUserId 外部联系人的userid
     * @apiSuccess (响应结果) {String} followUserList.remark 企业成员-->外部联系人      备注
     * @apiSuccess (响应结果) {String} followUserList.description 企业成员-->外部联系人      描述
     * @apiSuccess (响应结果) {Number} followUserList.createtime 企业成员-->外部联系人      时间
     * @apiSuccess (响应结果) {String} followUserList.remarkCorpName 企业成员-->外部联系人      备注的企业名称
     * @apiSuccessExample 响应结果示例
     * {"externalUserProfileList":[{"profileMap":{},"name":"owXZu4OP","type":"l66xlaULw"}],"followUserList":[{"createtime":3781468778185,"externalUserId":"IdYrQtj4","description":"e4","remark":"MDHi4d","remarkCorpName":"zHnPdR0L19","userid":"FEXKNxdT"}]}
     */
    @GetMapping("/getexternaluser")
    @ResponseBody
    public ExternalUserInfoDTO getExternalUser(String externalUserId, String companyNo) {
        String companyNoEnum=companyNo == null || companyNo.trim().isEmpty() ? Constants.DEFAULT_COMPANY_NO : companyNo;
        if(companyNoEnum==null){
            log.error("companyNo:{},getExternalUser,externalUserId:{},企微-企业主体不能为空！",companyNo,externalUserId);
            return null;
        }
        return wechatExternalUserService.getExternalUser(externalUserId, companyNoEnum.getCode());
    }

}