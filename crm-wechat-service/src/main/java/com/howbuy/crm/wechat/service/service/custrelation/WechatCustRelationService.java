/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.custrelation;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.response.consultantinfo.CmConsultantInfo;
import com.howbuy.crm.wechat.client.base.Response;
import com.howbuy.crm.wechat.client.domain.request.wechatrelation.QueryWechatRelationBatchRequest;
import com.howbuy.crm.wechat.client.domain.request.wechatrelation.QueryWechatRelationRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatrelation.QueryWechatRelationBatchResponse;
import com.howbuy.crm.wechat.client.domain.response.wechatrelation.QueryWechatRelationResponse;
import com.howbuy.crm.wechat.client.domain.response.wechatrelation.RelationInfo;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.client.producer.userrelation.request.QueryWechatUserRelationRequest;
import com.howbuy.crm.wechat.client.producer.userrelation.response.QueryWechatUserRelationResponse;
import com.howbuy.crm.wechat.dao.bo.CmWechatCustRelationBO;
import com.howbuy.crm.wechat.dao.mapper.CmWechatCustRelationMapper;
import com.howbuy.crm.wechat.dao.mapper.CmWechatEmpMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO;
import com.howbuy.crm.wechat.dao.po.CmWechatCustRelationPO;
import com.howbuy.crm.wechat.dao.po.CmWechatRefreshConscustPO;
import com.howbuy.crm.wechat.dao.po.custrelation.CustConsultRelationPO;
import com.howbuy.crm.wechat.dao.vo.custrelation.CustConsultRelationVO;
import com.howbuy.crm.wechat.service.commom.utils.LoggerUtils;
import com.howbuy.crm.wechat.service.commom.utils.ObjectUtils;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserInfoDTO;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserRelationInfoDTO;
import com.howbuy.crm.wechat.service.outerservice.crm.CrmAccountOuterService;
import com.howbuy.crm.wechat.service.outerservice.crm.QueryCustconstantInfoOuterService;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WechatExternalContactOuterService;
import com.howbuy.crm.wechat.service.repository.CmWechatCustInfoRepository;
import com.howbuy.crm.wechat.service.repository.CmWechatCustRelationRepository;
import com.howbuy.crm.wechat.service.repository.CmWechatRefreshConscustRepository;
import com.howbuy.crm.wechat.service.service.externalcontact.ChangeExternalContactEventService;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: (客户 vs 投顾 企业微信关系 service)
 * <AUTHOR>
 * @date 2023/11/1 13:23
 * @since JDK 1.8
 */

@Slf4j
@Service
public class WechatCustRelationService {

    /**
     * 342	零售金融产品与营销部
     */
    public static final String RETEAIL_PROD_YX_DEPT = "342";
    /**
     *650	零售研习社虚拟助理
     */
    public static final String YXS_VITRUAL_ASSITANT = "650";
    @Autowired
    private CmWechatCustRelationMapper wechatCustRelationMapper;
    @Autowired
    private CmWechatCustInfoRepository cmWechatCustInfoRepository;
    @Autowired
    private CrmAccountOuterService crmAccountOuterService;
    @Autowired
    private WechatExternalContactOuterService wechatExternalContactOuterService;
    @Resource
    @Lazy
    private ChangeExternalContactEventService changeExternalContactEventService;
    @Autowired
    private QueryCustconstantInfoOuterService queryCustconstantInfoOuterService;
    @Autowired
    private CmWechatCustRelationRepository cmWechatCustRelationRepository;
    @Autowired
    private CmWechatEmpMapper cmWechatEmpMapper;
    @Resource(name = "updateAllCustInfoExecutor")
    private ThreadPoolTaskExecutor updateAllCustInfoExecutor;
    @Resource(name = "dealSingleExecutor")
    private ThreadPoolTaskExecutor dealSingleExecutor;
    @Autowired
    private CmWechatRefreshConscustRepository cmWechatRefreshConscustRepository;

    @Value("${deptIdList}")
    private ArrayList<String> deptIdList;


    /**
     * @description:(根据 hboneNo vs conscode 批量查询客户投顾关联关系)
     * @param companyNoEnum  企微-企业主体
     * @param voList
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.custrelation.CustConsultRelationPO>
     * @author: haoran.zhang
     * @date: 2023/11/27 9:57
     * @since JDK 1.8
     */
    public List<CustConsultRelationPO> selectRelationListByVo(String companyNo,
                                                              List<CustConsultRelationVO> voList){
        //NOTICE : 该查询不支持 多companyNo . 所以不允许在 CustConsultRelationVO 中传入 companyNo
        Assert.hasText(companyNo, "企微-企业主体不能为空！");
        Assert.notEmpty(voList,"voList is empty");
        List<CustConsultRelationPO> resultList= Lists.newArrayList();
        //参数过多，分批查询
        List<List<CustConsultRelationVO>> pageList= Lists.partition(voList,1000);
        pageList.forEach(page-> resultList.addAll(wechatCustRelationMapper.selectRelationListByVo(companyNo,page)));
        return resultList;
    }

    /**
     * @description:(更新所有高端客户微信信息  和   企业微信客户投顾关联关系)
     * @param companyNoEnum  企微-企业主体
     * @return java.lang.String
     * @author: shuai.zhang
     * @date: 2024/3/26 14:29
     * @since JDK 1.8
     */
    public String updateAllCustInfoList(String companyNo) {
        //CM_WECHAT_REFRESH_CONSCUST  投顾客户微信刷新状态表
        String dealDt = DateUtil.getDateYYYYMMDD();
        log.info("companyNo:{}, updateAllCustInfoList处理开始:{}", companyNo,dealDt);

        //初始化当天需要处理的数据
        List<CmWechatRefreshConscustPO> notDeal = initNeedDeal(companyNo,dealDt);
        log.info("companyNo:{}, dealDt:{}，待处理条数：{}", companyNo,dealDt,notDeal.size());

        // 客户-微信划转客户map k-客户id v-划转投顾ids
        Map<String, List<ExternalUserRelationInfoDTO>> externalIdUserIdsMap = new ConcurrentHashMap<>();

        //循环挨个获取
        if (CollectionUtils.isNotEmpty(notDeal)) {
            String uuid = LoggerUtils.getUuid();
            //打印updateAllCustInfoExecutor线程池信息日志
            printThreadPoolLog();
            List<Future<Integer>> futures = new ArrayList<>();
            for (CmWechatRefreshConscustPO dto : notDeal) {
                Future<Integer> future = updateAllCustInfoExecutor.submit(() -> {
                    LoggerUtils.setChildUUID(uuid);
                    try {
                        dealSingle(companyNo,dto, externalIdUserIdsMap);
                        return 1;
                    } catch (Throwable e) {
                        log.error("companyNo:{}, 单个处理企微用户失败,user={}, error:{}",
                                companyNo,JSON.toJSONString(dto), Throwables.getStackTraceAsString(e));
                        return 0;
                    }
                });
                futures.add(future);
            }
            for (Future<Integer> future : futures) {
                try {
                    Integer result = future.get(3600, TimeUnit.SECONDS);
                    log.info("companyNo:{}, Task result: {}", companyNo,result);
                } catch (Exception e) {
                    log.error("companyNo:{}, Task execution error: {}",companyNo, Throwables.getStackTraceAsString(e));
                }
            }
        }
        // 处理管理员分配客户状态 externalIdUserIdsMap在此处处理
        handleTakeoutCustRelation(companyNo,externalIdUserIdsMap);
        externalIdUserIdsMap.clear();
        //该批次的数据处理完成后，置为废弃状态
        archieveCurrentBatchtDate(companyNo,dealDt);
        log.info("companyNo:{}, updateAllCustInfoList处理结束:{}", companyNo,DateUtil.getDateYYYYMMDD());
        return "success";
    }

    /**
     * @description: 打印线程池信息
     * @return void
     * @author: hongdong.xie
     * @date: 2025/4/2 19:32
     * @since JDK 1.8
     */
    private void printThreadPoolLog() {
        try {
            log.info("[updateAllCustInfoExecutor ThreadPool Status] " +
                            "CorePoolSize: {}, " +
                            "MaximumPoolSize: {}, " +
                            "CurrentPoolSize: {}, " +
                            "ActiveThreads: {}, " +
                            "QueueSize: {}",
                    updateAllCustInfoExecutor.getCorePoolSize(),
                    updateAllCustInfoExecutor.getMaxPoolSize(),
                    updateAllCustInfoExecutor.getPoolSize(),
                    updateAllCustInfoExecutor.getActiveCount(),
                    updateAllCustInfoExecutor.getThreadPoolExecutor().getQueue().size());
            log.info("[dealSingleExecutor ThreadPool Status] " +
                            "CorePoolSize: {}, " +
                            "MaximumPoolSize: {}, " +
                            "CurrentPoolSize: {}, " +
                            "ActiveThreads: {}, " +
                            "QueueSize: {}",
                    dealSingleExecutor.getCorePoolSize(),
                    dealSingleExecutor.getMaxPoolSize(),
                    dealSingleExecutor.getPoolSize(),
                    dealSingleExecutor.getActiveCount(),
                    dealSingleExecutor.getThreadPoolExecutor().getQueue().size());
        } catch (Exception e) {
            log.error("printThreadPoolLog error:{}", Throwables.getStackTraceAsString(e));
        }

    }

    /**
     * @description 集合分页
     * @param allList
     * @param pageSize
     * @return
     * <AUTHOR>
     * @date 2024/6/18 5:40 PM
     * @since JDK 1.8
     */
    public List<List<CmWechatRefreshConscustPO>> batchList(List<CmWechatRefreshConscustPO> allList, int pageSize) {
        List<List<CmWechatRefreshConscustPO>> resList = new ArrayList<>();
        for (int i = 0; i < allList.size(); i+= pageSize) {
            resList.add(allList.subList(i, Math.min(i + pageSize, allList.size())));
        }
        return resList;
    }




    /**
     * @description 该批次的数据处理完成后，置为废弃状态
     * @param companyNoEnum  企微-企业主体
     * @param nowDate
     * @return
     * <AUTHOR>
     * @date 2024/6/18 4:27 PM
     * @since JDK 1.8
     */
    private void archieveCurrentBatchtDate(String companyNo,
                                           String nowDate) {
        List<CmWechatRefreshConscustPO> notDealend = cmWechatRefreshConscustRepository.getRefreshTask(companyNo,nowDate, "0", "1");
        //获取刷新状态表中当天的已处理和未处理数据
        //未处理为0 说明已经处理完了   就全都更新为状态2 处理完废弃
        if (CollectionUtils.isEmpty(notDealend)) {
            //NOTICE: 历史逻辑问题FIX 2025年7月25日 。 where条件。 未约束。 dealStatus=0-未处理 . 导致当天重复执行。 重复更新
            // 修复：该方法命名，以及逻辑调整为： 更新处理任务状态，从1-已处理  更新为：2-处理完废弃
            int i = cmWechatRefreshConscustRepository.discardRefreshTask(companyNo,nowDate, "1");
            log.info("companyNo:{},当前任务处理完成，废弃条数：{}", companyNoEnum,i);
        }
    }

    /**
     * @description
     * @param companyNoEnum  企微-企业主体
     * @param dto
     * @param externalIdUserIdsMap
     * @return
     * <AUTHOR>
     * @date 2024/6/18 4:15 PM
     * @since JDK 1.8
     */
    private void dealSingle(String companyNo ,
                            CmWechatRefreshConscustPO dto,
                            Map<String, List<ExternalUserRelationInfoDTO>> externalIdUserIdsMap) {
        long startTime = System.currentTimeMillis();
        String wechatconscode = dto.getDealData();

//        String companyNoEnum=Constants.DEFAULT_COMPANY_NO;

        //先获取投顾添加的所有客户
        List<String> externalUserIdList = wechatExternalContactOuterService.getExternalUserIdList(wechatconscode, companyNoEnum);
        log.info("companyNoEnum:{}, updateAllCustInfoList更新列表:投顾:{}, 所有客户个数:{}, 获取客户列表耗时:{}ms",
            companyNoEnum,
            wechatconscode,
            (null == externalUserIdList ? 0 : externalUserIdList.size()),
            System.currentTimeMillis() - startTime);

        if (CollectionUtils.isNotEmpty(externalUserIdList)) {
            long batchStartTime = System.currentTimeMillis();
            if (externalUserIdList.size() > 500) {
                // 分批处理
                List<List<String>> batchLists = Lists.partition(externalUserIdList, 500);
                log.info("companyNoEnum:{}, 分批处理,投顾：{} 总批次数:{}",companyNoEnum,wechatconscode, batchLists.size());
                String batchUuid = LoggerUtils.getUuid();
                List<Future<Integer>> futures = new ArrayList<>();
                for (int i = 0; i < batchLists.size(); i++) {
                    List<String> batchUserIdList = batchLists.get(i);
                    int finalI = i;
                    Future<Integer> future = dealSingleExecutor.submit(() -> {
                        LoggerUtils.setChildUUID(batchUuid);
                        log.info("companyNoEnum:{}, 处理批次数据开始，投顾：{}, 批次:{}",companyNoEnum,wechatconscode, finalI);
                        try {
                            long processBatchStartTime = System.currentTimeMillis();
                            processBatch(companyNoEnum,batchUserIdList, wechatconscode, externalIdUserIdsMap);
                            log.info("companyNoEnum:{}, 处理批次数据完成, 批次大小:{}, 耗时:{}ms",
                                    companyNoEnum,batchUserIdList.size(),
                                System.currentTimeMillis() - processBatchStartTime);
                        } catch (Exception e) {
                            log.error("companyNoEnum:{}, 处理批次数据失败, wechatconscode={}, error={}",
                                    companyNoEnum,
                                    wechatconscode,
                                    Throwables.getStackTraceAsString(e));
                            return 0;
                        }
                        log.info("companyNoEnum:{}, 处理批次数据结束，投顾：{}, 批次:{}",
                                companyNoEnum,wechatconscode, finalI);
                        return 1;
                    });
                    futures.add(future);
                }

                try {
                    for (Future<Integer> future : futures) {
                        Integer result = future.get(60, TimeUnit.MINUTES);
                        if (result == 0) {
                            log.error("companyNoEnum:{}, 批次处理失败, 投顾：{}", companyNoEnum, wechatconscode);
                        }
                    }
                    log.info("companyNoEnum:{}, 所有批次处理完成, 总批次数:{}, 总耗时:{}ms", companyNoEnum,batchLists.size(),
                        System.currentTimeMillis() - batchStartTime);
                } catch (Exception e) {
                    log.error("companyNoEnum:{}, 等待批次处理完成失败, error={}", companyNoEnum, Throwables.getStackTraceAsString(e));
                }
            } else {
                log.info("companyNoEnum:{}, 直接处理开始，投顾：{}",companyNoEnum, wechatconscode);
                // 直接处理
                processBatch(companyNoEnum,externalUserIdList, wechatconscode, externalIdUserIdsMap);
                log.info("companyNoEnum:{}, 直接处理完成，投顾:{}, 耗时:{}ms",
                        companyNoEnum,wechatconscode,System.currentTimeMillis() - batchStartTime);
            }
        }

        //更新刷新表状态并提交
        CmWechatRefreshConscustPO tempPo = new CmWechatRefreshConscustPO();
        tempPo.setId(dto.getId());
        tempPo.setDealStatus(YesOrNoEnum.YES.getCode());
        tempPo.setUpdateTime(new Date());
//        tempPo.setCompanyNo(companyNoEnum.getCode()); //无需更新
        cmWechatRefreshConscustRepository.updateByPrimaryKeySelective(tempPo);

        log.info("companyNoEnum:{}, 投顾:{} 处理完成, 总耗时:{}ms",
                companyNoEnum,
                wechatconscode,
                System.currentTimeMillis() - startTime);
    }

    /**
     * 批量处理单个企微用户数据
     * @param companyNoEnum 企微-企业主体
     * @param externalUserIds
     * @param wechatconscode
     * @param externalIdUserIdsMap
     */
    private void processBatch(String companyNoEnum,
                              List<String> externalUserIds,
                              String wechatconscode,
                              Map<String, List<ExternalUserRelationInfoDTO>> externalIdUserIdsMap) {

        for (String externalUserId : externalUserIds) {
            try {
                ExternalUserInfoDTO externalUser = wechatExternalContactOuterService.getExternalUser(externalUserId, companyNoEnum, false);
                //国内一账通,通过unionid调用账户中心接口获取一账通信息
                String hboneNo = changeExternalContactEventService.getHboneNoByAccCenter(externalUser.getUnionid());
                //通过uninonId 查询香港账户中心获取一帐通信息
                String hkhboneNo = changeExternalContactEventService.getHkhboneNoByHkAcccenter(externalUser.getUnionid());
                //如果一账通号不为空, 则调用企业微信接口修改该客户在改投顾的备注
                if (StringUtils.isNotEmpty(hboneNo)) {
                    externalUser.setHboneNo(hboneNo);
                }
                if (StringUtils.isNotEmpty(hkhboneNo)) {
                    externalUser.setHkHboneNo(hkhboneNo);
                }
                log.info("companyNo:{}, insertCmWechatCustInfo开始：hboneno={}, externalUserId={}",
                        companyNoEnum,externalUser.getHboneNo(), externalUser.getExternalUserId());
                cmWechatCustInfoRepository.insertCmWechatCustInfo(
                        changeExternalContactEventService.setWechatCustInfo(externalUser),
                        companyNoEnum);
                List<ExternalUserRelationInfoDTO> followUserList = externalUser.getFollowUserList();
                if (CollectionUtils.isNotEmpty(followUserList)) {
                    // 筛选出 所有管理员后台 分配的客户-投顾信息 202为管理员后台分配   存入externalIdUserIdsMap  后续处理管理员划转逻辑
                    extractFollowersByAdmin(externalUserId, followUserList, externalIdUserIdsMap);
                    //匹配到当前投顾  更新客户关系表
                    updateCurConsCodeCustRelation(companyNoEnum,followUserList, wechatconscode);
                    log.info("companyNo:{}, 处理单个外部用户完成：wechatconscode={}，外部联系人：{}",
                            companyNoEnum,wechatconscode,JSON.toJSONString(followUserList));
                }
            } catch (Exception e) {
                log.error("companyNo:{}, 处理单个外部用户失败, externalUserId={}, error={}",
                        companyNoEnum,externalUserId, Throwables.getStackTraceAsString(e));
            }
        }
    }


    /**
     * 根据公司编号获取 额外需要 定时任务待同步的 投顾部门列表
     * @param companyNoEnum  企微-企业主体
     */
    private List<String> getExtraSyncDeptList(String companyNoEnum){
        if(companyNoEnum == Constants.DEFAULT_COMPANY_NO){
//            342	零售金融产品与营销部
//            650	零售研习社虚拟助理
            return deptIdList;
            //Lists.newArrayList(RETEAIL_PROD_YX_DEPT, YXS_VITRUAL_ASSITANT);
        }
        return Lists.newArrayList();
    }

    /**
     * @description 初始化当天需要处理的数据
     * @param companyNoEnum  企微-企业主体
     * @param dealDt 处理日期
     * @return
     * <AUTHOR>
     * @date 2024/6/18 3:42 PM
     * @since JDK 1.8
     */
    private List<CmWechatRefreshConscustPO> initNeedDeal(String  companyNoEnum, String dealDt) {
        String companyNo = companyNoEnum.getCode();
        //1.获取刷新状态表中当天的已处理和未处理数据
        List<CmWechatRefreshConscustPO> deal = cmWechatRefreshConscustRepository.getRefreshTask(companyNo,dealDt, "1", "1");
        List<CmWechatRefreshConscustPO> notDeal = cmWechatRefreshConscustRepository.getRefreshTask(companyNo,dealDt, "0", "1");
        log.info("WechatCustRelationService|updateAllCustInfoList,deal:{},notDeal:{}", deal.size(), notDeal.size());
        //2.都为0  抓所有投顾微信进表并处理
        //3.否则   未处理不为0    就处理未处理的数据
        if (CollectionUtils.isEmpty(deal) && CollectionUtils.isEmpty(notDeal)) {
            //调用crm-account接口 获取所有需要刷新的投顾code列表
            //TODO: 重构计划标记： 如果支持多个企业。 crm数据库投顾表 目前 只有 【好买财富】的企微账号。 需要改成支持多个企微账号
            // 需要  crm投顾 vs 企微企业员工  并集 的逻辑.

            List<String> refreshConsCodeList = crmAccountOuterService.getAllNeedRefreshWechatConsCode();
            //增加mysql库里342,650两个部门的员工
            List<String> syncDeptList = getExtraSyncConscodeList(companyNoEnum);
            refreshConsCodeList.addAll(syncDeptList);
            refreshConsCodeList = refreshConsCodeList.stream().distinct().collect(Collectors.toList());
            int count = cmWechatRefreshConscustRepository.insertRefreshTask(companyNo,dealDt, "1", refreshConsCodeList);
            log.info("companyNoEnum：{}，WechatCustRelationService|updateAllCustInfoList,insertCmWechatRefreshConscustNowDate,nowDate:{},count:{}",
                    companyNoEnum,dealDt, count);
            notDeal = cmWechatRefreshConscustRepository.getRefreshTask(companyNo,dealDt, "0", "1");
        }
        return notDeal;
    }


    /**
     * 获取额外需要处理的外部联系人
     * @param companyNoEnum
     * @return List<String>
     */
    private List<String> getExtraSyncConscodeList(String companyNoEnum){
        String companyNo = companyNoEnum.getCode();
        List<String> syncDeptList = getExtraSyncDeptList(companyNoEnum);
        if(CollectionUtils.isEmpty(syncDeptList)){
            return Lists.newArrayList();
        }
        List<String> empList = cmWechatEmpMapper.selectEmpIdByDeptList(companyNo,syncDeptList);
        log.info("companyNoEnum：{}，额外增加部门:{}下的，投顾列表：{}", companyNoEnum,JSON.toJSONString(syncDeptList),JSON.toJSONString(empList));
        return empList;
    }

    /**
     * @description 筛选出 所有管理员后台 分配的客户-投顾信息 202为管理员后台分配
     * @param externalUserId
     * @param followUserList
     * @param externalIdUserIdsMap
     * @return
     * <AUTHOR>
     * @date 2024/6/18 3:38 PM
     * @since JDK 1.8
     */
    private void extractFollowersByAdmin(String externalUserId,
                                         List<ExternalUserRelationInfoDTO> followUserList,
                                         Map<String, List<ExternalUserRelationInfoDTO>> externalIdUserIdsMap) {
        List<ExternalUserRelationInfoDTO> relationUsers = followUserList.stream()
                .filter(v -> "202".equals(v.getAddWay()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(relationUsers)) {
            externalIdUserIdsMap.put(externalUserId, relationUsers);
        }
    }

    /**
     * @description 更新当前投顾的客户关系数据
     * @param companyNoEnum  企微-企业主体
     * @param followUserList
     * @param wechatConscode
     * @return
     * <AUTHOR>
     * @date 2024/6/18 3:35 PM
     * @since JDK 1.8
     */
    private void updateCurConsCodeCustRelation(String companyNoEnum,
                                               List<ExternalUserRelationInfoDTO> followUserList,
                                               String wechatConscode) {
        List<ExternalUserRelationInfoDTO> curRelationInfos = followUserList.stream().filter(f -> wechatConscode.equals(f.getUserid())).collect(Collectors.toList());
        for (ExternalUserRelationInfoDTO curRelationInfo : curRelationInfos) {
            String realConsCode = queryCustconstantInfoOuterService.queryConsultantByWeChatConsCode(wechatConscode);
            if (StringUtils.isNotEmpty(realConsCode)) {
                curRelationInfo.setUserid(realConsCode);
            }
            cmWechatCustRelationRepository.insertCmWechatCustRelationForTask(curRelationInfo, companyNoEnum);
        }
    }

    /**
     * @description:(处理管理员后台分配投顾，同步客户企微关系表状态)
     * @param companyNoEnum  企微-企业主体
     * @param externalIdUserIdsMap	 已分配客户投顾map k-外部客户id v-投顾列表
     * @return void
     * @author: shuai.zhang
     * @date: 2024/3/27 18:34
     * @since JDK 1.8
     */
    private void handleTakeoutCustRelation(String companyNoEnum,
                                           Map<String, List<ExternalUserRelationInfoDTO>> externalIdUserIdsMap) {
        if (MapUtils.isEmpty(externalIdUserIdsMap)) {
            return;
        }

        Set<String> externalIds = externalIdUserIdsMap.keySet();
        log.info("companyNo:{}, handleTakeoutCustRelation externalIdUserIdsMap.keys={}", companyNoEnum,externalIds);

        // 分批处理，每批1000条
        List<String> externalIdList = new ArrayList<>(externalIds);
        List<List<String>> batchLists = Lists.partition(externalIdList, 1000);
        List<CmWechatCustRelationPO> relationlist = new ArrayList<>();

        for (List<String> batch : batchLists) {
            List<CmWechatCustRelationPO> batchResult = cmWechatCustRelationRepository.getRealtionListByExternalUserId(
                    companyNoEnum,
                    new ArrayList<>(batch)
            );
            if (CollectionUtils.isNotEmpty(batchResult)) {
                relationlist.addAll(batchResult);
            }
            log.info("companyNo:{}, handleTakeoutCustRelation batch process, batchSize={}, resultSize={}",
                companyNoEnum,
                batch.size(),
                batchResult != null ? batchResult.size() : 0);
        }

        List<CmWechatCustRelationPO> updateRelationInfos = new ArrayList<>();
        if (CollectionUtils.isEmpty(relationlist)) {
            return;
        }

        List<String> conscodeList = relationlist.stream().map(CmWechatCustRelationPO::getConscode).collect(Collectors.toList());
        List<CmConsultantInfo> mapList = crmAccountOuterService.getWechatConscodesByConscodes(conscodeList);
        if (CollectionUtils.isEmpty(mapList)) {
            return;
        }

        Map<String, String> consCodeUserIdMap = new HashMap<>();
        mapList.forEach(v -> consCodeUserIdMap.put(v.getConscode(), v.getWechatconscode()));
        Map<String, List<CmWechatCustRelationPO>> externalUserRelationMap = relationlist.stream()
                .collect(Collectors.groupingBy(CmWechatCustRelationPO::getExternalUserId));
        log.info("companyNo:{}, handleTakeoutCustRelation externalUserRelationMap.keys={}", companyNoEnum,externalUserRelationMap.keySet());
        externalIdUserIdsMap.forEach((externalId, list) -> {
            List<CmWechatCustRelationPO> cmWeChatCustRelationInfos = externalUserRelationMap.get(externalId);
            if (CollectionUtils.isEmpty(cmWeChatCustRelationInfos)) {
                return;
            }

            // 遍历企微接口查询的最新关系
            list.forEach(externalUserRelationInfo -> {
                // 遍历当前库中的关系表
                for (CmWechatCustRelationPO oldRelationInfo : cmWeChatCustRelationInfos) {
                    boolean result = checkCustTransferResult(companyNoEnum,
                            externalId,
                            consCodeUserIdMap.get(oldRelationInfo.getConscode()),
                            externalUserRelationInfo.getUserid(),
                            externalUserRelationInfo.getCreatetime());
                    log.info("companyNo:{}, handleTakeoutCustRelation checkCustTransferResult params: handoverUserid=[{}], takeoverUserid=[{}], date=[{}], result=[{}]",
                            companyNoEnum, oldRelationInfo.getConscode(), externalUserRelationInfo.getUserid(), externalUserRelationInfo.getCreatetime(), result);
                    if (result) {
                        updateRelationInfos.add(oldRelationInfo);
                    }
                }
            });
        });

        if (CollectionUtils.isEmpty(updateRelationInfos)) {
            return;
        }

        for (CmWechatCustRelationPO info : updateRelationInfos) {
            int result = cmWechatCustRelationRepository.updateCmWechatCustRelationDel(info);
            log.info("companyNo:{}, handleTakeoutCustRelation updateCmWechatCustRelation conscode=[{}], externalId=[{}], result=[{}]",
                    companyNoEnum,info.getConscode(), info.getExternalUserId(), result);
        }
    }

    /**
     * 查询客户接替状态
     * @param companyNoEnum  企微-企业主体
     * @param externalId     外部联系人userid
     * @param handoverUserid 原添加成员的userid
     * @param takeoverUserid 接替成员的userid
     * @param date 转入分配时间
     * @return boolean 接替结果状态 true-划出 false-未划出
     */
    public boolean checkCustTransferResult(String companyNoEnum,
                                           String externalId,
                                           String handoverUserid,
                                           String takeoverUserid,
                                           Date date) {
        if (companyNoEnum==null ||
            StringUtil.isEmpty(handoverUserid) ||
            StringUtil.isEmpty(takeoverUserid) ||
            null == date ||
            StringUtil.isEmpty(externalId)  ||
            handoverUserid.equals(takeoverUserid)) {
            return false;
        }

        try {
            //TODO: 重构计划标记： 解析企微返回结果， 应在outer层处理。 不应此处处理
            String retMsg = wechatExternalContactOuterService.getTransferResult(handoverUserid, takeoverUserid,companyNoEnum);
            if(StringUtil.isEmpty(retMsg)){
                return false;
            }

            JSONObject json = JSONObject.parseObject(retMsg);
            if (json.isEmpty() || !json.containsKey("customer")) {
                return false;
            }

            JSONArray customer = json.getJSONArray("customer");
            if (CollectionUtils.isEmpty(customer)) {
                return false;
            }
            long count = customer.stream()
                    .filter(v -> {
                        JSONObject data = (JSONObject) v;
                        // 客户的外部联系人userid
                        String externalUserId = data.getString("external_userid");
                        // 状态 status 1-接替完毕 2-等待接替 3-客户拒绝 4-接替成员客户达到上限
                        boolean status = "1".equals(data.getString("status"));
                        // 接替客户的时间，如果是等待接替状态，则为未来的自动接替时间
                        Date takeoverTime = ObjectUtils.trimNullDateMulK(data.get("takeover_time"));
                        // 当属于该外部用户 且 状态为接替完毕 且 接替时间为传入时间时，返回true
                        return externalId.equals(externalUserId) && status && date.equals(takeoverTime);
                    })
                    .count();
            return count > 0;
        } catch (Exception e) {
            log.error("companyNo:{}, checkCustTransferResult 查询继承接替结果失败 error={}", companyNoEnum,e.getMessage(), e);
        }

        return false;
    }

    /**
     * @description: 根据一账通号查询客户与企业微信用户关系
     * @param request
     * @return com.howbuy.crm.wechat.client.base.Response<com.howbuy.crm.wechat.client.producer.userrelation.response.QueryWechatUserRelationResponse>
     * @author: hongdong.xie
     * @date: 2024/8/30 18:41
     * @since JDK 1.8
     */
    public Response<QueryWechatUserRelationResponse> queryWechatUserRelation(QueryWechatUserRelationRequest request) {

        //入口 companyNo 默认赋值：
        String companyNoEnum = request.getCompanyNo();
        if (companyNoEnum == null) {
            companyNoEnum=Constants.DEFAULT_COMPANY_NO;
        }

        String hboneNo = request.getHboneNo();
        // 一账通号必传
        if (StringUtil.isEmpty(hboneNo)){
            return new Response<>(ResponseCodeEnum.PARAM_ERROR.getCode(), ResponseCodeEnum.PARAM_ERROR.getDescription(), null);
        }
        List<String> userIdList = request.getUserIdList();
        CmWechatCustInfoPO infoPO =  cmWechatCustInfoRepository.getExternalUserByHboneNo(hboneNo, companyNoEnum);
        if (infoPO == null) {
            log.info("queryWechatUserRelation hboneNo={} not found", hboneNo);
            return Response.ok(new QueryWechatUserRelationResponse());
        }
        // 查询客户与企业微信用户关系
        List<CmWechatCustRelationPO> relationPOList = cmWechatCustRelationRepository.getByExternalIdAndUserIdsList(infoPO.getExternalUserId(), userIdList, infoPO.getCompanyNo());
        QueryWechatUserRelationResponse response = new QueryWechatUserRelationResponse();
        List<QueryWechatUserRelationResponse.UserRelationVO> userRelationList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(relationPOList)) {
            response.setNickName(infoPO.getNickName());
            for (CmWechatCustRelationPO relationPO : relationPOList) {
                QueryWechatUserRelationResponse.UserRelationVO userRelationVO = new QueryWechatUserRelationResponse.UserRelationVO();
                userRelationVO.setUserId(relationPO.getConscode());
                userRelationVO.setExternalUserId(relationPO.getExternalUserId());
                userRelationVO.setAddTime(relationPO.getAddTime());
                userRelationVO.setDeleteTime(relationPO.getDelTime());
                userRelationVO.setStatus(relationPO.getStatus());
                // 首次添加时间(2025-07-08)，历史数据默认使用创建时间
                userRelationVO.setFirstAddTime(Objects.nonNull(relationPO.getFirstAddTime()) ? relationPO.getFirstAddTime() : relationPO.getCreateTime());
                userRelationList.add(userRelationVO);
            }
            response.setUserRelationList(userRelationList);
        }

        return Response.ok(response);
    }

    /**
     * @param request
     * @return com.howbuy.crm.wechat.client.base.Response<com.howbuy.crm.wechat.client.domain.response.wechatrelation.QueryWechatRelationByExternalIdsResponse>
     * @description:(查询数据)
     * @author: xufanchao
     * @since JDK 1.8
     */
    public Response<QueryWechatRelationResponse> queryWechatRelation(QueryWechatRelationRequest request) {
        String companyNoEnum= request.getCompanyNo();
        if (companyNoEnum == null) {
            //入口 companyNo 默认赋值：
            companyNoEnum=Constants.DEFAULT_COMPANY_NO;
        }
        String companyNo = companyNoEnum.getCode();
        CmWechatCustRelationBO cmWechatCustRelationBO =
                cmWechatCustRelationRepository.getByHbOneNoAndConscode(
                companyNo,
                request.getHboneNo(),
                request.getConscode());

        QueryWechatRelationResponse response = new QueryWechatRelationResponse();
        if (cmWechatCustRelationBO != null) {
            response.setGnUnionId(cmWechatCustRelationBO.getGnUnionId());
            response.setHwUnionId(cmWechatCustRelationBO.getHwUnionId());
            response.setCompanyNo(companyNo);
        }
        return Response.ok(response);
    }

    /**
     * @param request 批量查询请求
     * @return com.howbuy.crm.wechat.client.base.Response<com.howbuy.crm.wechat.client.domain.response.wechatrelation.QueryWechatRelationBatchResponse>
     * @description: 批量查询客户与投顾的企业微信关系
     * @author: xufanchao
     * @date: 2024/5/23 11:12
     * @since JDK 1.8
     */
    public Response<QueryWechatRelationBatchResponse> queryWechatRelationBatch(QueryWechatRelationBatchRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getHbOneAndConsCodeDtoList())) {
            return new Response<>(ResponseCodeEnum.PARAM_ERROR.getCode(), ResponseCodeEnum.PARAM_ERROR.getDescription(), null);
        }
        String companyNoEnum= request.getCompanyNo();
        if (companyNoEnum == null) {
            //入口 companyNo 默认赋值：
            companyNoEnum=Constants.DEFAULT_COMPANY_NO;
        }
        String companyNo = companyNoEnum.getCode();

        // 将请求参数转换为VO对象
        List<CustConsultRelationVO> voList = request.getHbOneAndConsCodeDtoList().stream().map(dto -> {
            CustConsultRelationVO vo = new CustConsultRelationVO();
            vo.setHboneNo(dto.getHbOneNo());
            vo.setConscode(dto.getConsCode());
            return vo;
        }).collect(Collectors.toList());

        // 分批处理，每批1000条
        List<RelationInfo> relationInfoList = new ArrayList<>();
        List<List<CustConsultRelationVO>> batchList = Lists.partition(voList, 500);

        for (List<CustConsultRelationVO> batch : batchList) {
            List<CmWechatCustRelationBO> relationBOList = cmWechatCustRelationRepository.getByHbOneNoAndConscodeBatch(companyNo,batch);
            if (CollectionUtils.isNotEmpty(relationBOList)) {
                for (CmWechatCustRelationBO bo : relationBOList) {
                    RelationInfo relationInfo = new RelationInfo();
                    relationInfo.setHbOneNo(bo.getHbOneNo());
                    relationInfo.setConsCode(bo.getConsCode());
                    relationInfo.setGnUnionId(bo.getGnUnionId());
                    relationInfo.setHkUnionId(bo.getHwUnionId());
                    relationInfo.setCompanyNo(companyNo);
                    relationInfoList.add(relationInfo);
                }
            }
        }

        QueryWechatRelationBatchResponse response = new QueryWechatRelationBatchResponse();
        response.setRelationInfoList(relationInfoList);
        return Response.ok(response);
    }
}