/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import com.google.common.collect.Lists;
import com.howbuy.crm.wechat.service.service.syncchatgroup.SyncChatGroupService;
import com.howbuy.message.SimpleMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 根据员工userId同步所有企微客户群定时任务
 * <AUTHOR>
 * @date 2023/10/25 17:26
 * @since JDK 1.8
 */
@Slf4j
@Component
public class SyncChatGroupJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列，nacos中需要配置sync.SYNC_CHAT_GROUP_JOB对应的队列名称
     */
    @Value("${sync_chat_cust_group_channel:TOPIC_SYNC_CHAT_CUST_GROUP_JOB}")
    private String queue;

    @Autowired
    private SyncChatGroupService syncChatGroupService;

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("SyncChatGroupJob process start");
        try {
            String msgContent = getContent(message);
            List<String> companyNoEnumList = parseSyncCompanyNoList(msgContent);

            syncChatGroupService.syncChatGroupData(companyNoEnumList);
        } catch (Exception e) {
            log.error("error in SyncChatGroupJob", e);
        }
        log.info("SyncChatGroupJob process end");
    }


    /**
     * @description: 解析入参 获取公司编码列表 默认为财富公司
     * @param msgContent 入参
     * @return java.util.List<java.lang.String> 公司编码列表
     * @author: jin.wang03
     * @date: 2023/10/31 9:15
     * @since JDK 1.8
     */
    private static List<String> parseSyncCompanyNoList(String msgContent) {
//        List<String> companyNoEnumList = new ArrayList<>();

        if (StringUtils.isNotBlank(msgContent)) {
            JSONObject argJson = JSON.parseObject(msgContent);
            String  paramCompanyNo = argJson.getString("companyNo");
            if (StringUtil.isNotEmpty(paramCompanyNo)) {
                return Arrays.stream(paramCompanyNo.split(",")).map(String::getEnum).collect(Collectors.toList());
            }
        }
//        if (CollectionUtils.isEmpty(companyNoEnumList)) {
//            companyNoEnumList.add(Constants.DEFAULT_COMPANY_NO);
//        }
        return Lists.newArrayList(Constants.DEFAULT_COMPANY_NO);
    }

    @Override
    protected String getQuartMessageChannel() {
        // 返回调度配置的队列名
        return queue;
    }
}