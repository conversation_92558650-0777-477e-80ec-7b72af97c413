package com.howbuy.crm.wechat.service.controller;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.aes.WXBizMsgCrypt;
import com.howbuy.crm.wechat.service.commom.utils.WechatCorpUtil;
import com.howbuy.crm.wechat.service.domain.callback.WechatCallbackSignatureDTO;
import com.howbuy.crm.wechat.service.service.WechatCallBackService;
import com.howbuy.crm.wechat.service.service.config.BaseConfigServce;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.Objects;

/**
 * @classname: WechatCallbackController
 * @author: yu.zhang
 * @description: 企业微信回调contorller
 * @creatdate: 2021-02-08 16:48
 * @since: JDK1.8
 */
@Slf4j
@RestController
@RequestMapping("/wechat")
public class WechatCallbackController {

    @Autowired
    private WechatCallBackService wechatCallBackService;

    @Autowired
    private BaseConfigServce baseConfigServce;



    /**
     * @api {GET} /wechat/companyverify companyVerify()
     * @apiVersion 1.0.0
     * @apiGroup WechatCallbackController
     * @apiName companyVerify()
     * @apiDescription 企业微信入口方法
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @RequestMapping(value = "/companyverify", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public void companyVerify(HttpServletRequest request, HttpServletResponse response) {
        //未被使用。 是否可行。  通过配置参数 compayNo=1/2  统一配置监听入口
        String companyNo = request.getParameter("companyNo");
        if (companyNo == null || companyNo.trim().isEmpty()) {
            companyNo = Constants.DEFAULT_COMPANY_NO;
        }
        this.verify(request, response, companyNo);
    }



    /**
     * @api {GET} /wechat/fundverify fundVerify()
     * @apiVersion 1.0.0
     * @apiGroup WechatCallbackController
     * @apiName fundVerify()
     * @apiDescription 好买基金企业微信入口方法
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @RequestMapping(value = "/fundverify", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public void fundVerify(HttpServletRequest request, HttpServletResponse response) {
//        NOTICE : 该url入口链接，被配置在企微应用配置中，接收企微信息回调。 企业：好买基金。
        this.verify(request, response, Constants.COMPANY_NO_HOWBUY_FUND);
    }


    /**
     * @api {GET} /wechat/hxmverify hxmVerify()
     * @apiVersion 1.0.0
     * @apiGroup WechatCallbackController
     * @apiName hxmVerify()
     * @apiDescription 好晓买企业微信入口方法
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @RequestMapping(value = "/hxmverify", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public void hxmVerify(HttpServletRequest request, HttpServletResponse response) {
//        NOTICE : 该url入口链接，被配置在企微应用配置中，接收企微信息回调。 企业：好买基金。
        this.verify(request, response, Constants.COMPANY_NO_HOWBUY_HXM);
    }

    /**
     * @api {GET} /wechat/wealthverify wealthVerify()
     * @apiVersion 1.0.0
     * @apiGroup WechatCallbackController
     * @apiName wealthVerify()
     * @apiDescription 好买财富企业微信入口方法
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @RequestMapping(value = "/wealthverify", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public void wealthVerify(HttpServletRequest request, HttpServletResponse response) {
//        NOTICE : 该url入口链接，被配置在企微应用配置中，接收企微信息回调。 企业：好买财富。
//                 目前未被使用， 消息入口依赖  老企微消息二次推送。
        this.verify(request, response, Constants.DEFAULT_COMPANY_NO);
    }

    /**
     * @description:企业微信回调验证方法+消息返回
     * @param request
     * @param response
     * @param companyNo 企微-企业主体
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/8 15:09
     * @since JDK 1.8
     */
    private void verify(HttpServletRequest request, HttpServletResponse response, String companyNo) {

        boolean isGet = "get".equalsIgnoreCase(request.getMethod());
        log.info("companyNo:{},接收企业微信消息方式:{}",companyNo, isGet);
        if (Objects.equals(Boolean.TRUE, isGet)) {
            this.sendVerifySuccess(request, response, companyNo);
        } else {
            this.acceptMessage(request, response, companyNo);
        }
    }
    /**
     * acceptMessage 触发回调，用于实际的业务请求，比如应用菜单的点击事件，用户消息等。当有回调的行为发生时，企业微信服务端会向该回调URL发起一个 Post 请求
     * @param request
     * @param response
     * @param companyNo 企微-企业主体
     * @return void
     * @Author: yu.zhang on 2021/2/9 11:18
     */
    private void acceptMessage(HttpServletRequest request, HttpServletResponse response, String companyNo) {
        log.info("companyNo:{},acceptMessage开始",companyNo);
        long s = System.currentTimeMillis();
        //1.将请求、响应的编码均设置为UTF-8（防止中文乱码）
        try {
            request.setCharacterEncoding("UTF-8");
            response.setCharacterEncoding("UTF-8");

            //1.解密微信发过来的消息
            WechatCallbackSignatureDTO wechatCallbackSignature = this.getDecryptMsg(request, companyNo);

            //2.调用消息业务类接收消息、处理消息
            String respMessage = wechatCallBackService.getEncryptRespMessage(wechatCallbackSignature, companyNo);

            //3.响应消息
            PrintWriter out = response.getWriter();
            out.print(respMessage);
            out.close();
            log.info("acceptMessage返回,耗时:{}", System.currentTimeMillis() - s);
        } catch (IOException e) {
            log.error("acceptMessage IOException返回:{}", e.getMessage());
        }
    }

    /**
     * @description:回调验证，仅用于在应用创建配置应用信息时的验证，企业微信服务端会向回调URL发起一个 Get 请求
     * @param request
     * @param response
     * @param companyNo 企微-企业主体
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/8 9:59
     * @since JDK 1.8
     */
    private void sendVerifySuccess(HttpServletRequest request,
                                   HttpServletResponse response,
                                   String companyNo) {
        String verifyMsgSig = request.getParameter("msg_signature");
        String verifyTimeStamp = request.getParameter("timestamp");
        String verifyNonce = request.getParameter("nonce");
        String verifyEchoStr = request.getParameter("echostr");
        String echoStr; //需要返回的明文

        log.info("GetMapping,签名串:{},时间戳:{},随机串:{},消息体:{}", verifyMsgSig, verifyTimeStamp, verifyNonce, verifyEchoStr);

        try {
            PrintWriter out = response.getWriter();
              WXBizMsgCrypt wxcpt = baseConfigServce.buildWXBizMsgCrypt(companyNo);
            //URL验证
            echoStr = wxcpt.VerifyURL(verifyMsgSig, verifyTimeStamp,
                    verifyNonce, verifyEchoStr);
            log.info("companyNo:{},sendVerifySuccess echoStr:{} ", companyNo,echoStr);
            out.print(echoStr);
            out.close();
        } catch (Exception e) {
            //验证URL失败，错误原因请查看异常
            log.error("sendVerifySuccess Exception:{}", e.getMessage());
        }
    }

    /**
     * @description:从request中获取消息明文
     * @param request
     * @param companyNo 企微-企业主体
     * @return com.howbuy.crm.wechat.service.domain.callback.WechatCallbackSignatureDTO
     * @author: yu.zhang
     * @date: 2023/6/8 15:24
     * @since JDK 1.8
     */
    private WechatCallbackSignatureDTO getDecryptMsg(HttpServletRequest request, String companyNo) {
        // 密文，对应POST请求的数据
        StringBuilder postData = new StringBuilder();
        // 明文，解密之后的结果
        String result = Strings.EMPTY;

        // 微信加密签名
        String signature = request.getParameter("msg_signature");
        String timestamp = request.getParameter("timestamp");
        String nonce = request.getParameter("nonce");

        WechatCallbackSignatureDTO signatureDTO = new WechatCallbackSignatureDTO();
        signatureDTO.setSignature(signature);
        signatureDTO.setTimestamp(timestamp);
        signatureDTO.setNonce(nonce);

        log.info("companyNo:{},getDecryptMsg开始,signatureDTO:{}",companyNo, JSON.toJSONString(signatureDTO));
        try {
            //1.获取加密的请求消息：使用输入流获得加密请求消息postData
            ServletInputStream in = request.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(in));
            //作为输出字符串的临时串，用于判断是否读取完毕
            String tempStr;
            while (null != (tempStr = reader.readLine())) {
                postData.append(tempStr);
            }

            log.info("companyNo:{},getDecryptMsg开始,postData:{}", companyNo,postData);

            //2.获取消息明文：对加密的请求消息进行解密获得明文
            WXBizMsgCrypt wxBizMsgCrypt = baseConfigServce.buildWXBizMsgCrypt(companyNo);
            result = wxBizMsgCrypt.DecryptMsg(signature, timestamp, nonce, postData.toString());

            signatureDTO.setResult(result);
        } catch (Exception e) {
            log.info("companyNo:{},getDecryptMsg开始,IOException:{}",companyNo, e.getMessage());
        }

        return signatureDTO;
    }
}
