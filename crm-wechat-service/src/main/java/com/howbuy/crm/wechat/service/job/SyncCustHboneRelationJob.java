/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.job;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.wechat.client.base.BaseCompanyNoRequest;
import com.howbuy.crm.wechat.service.service.custrelation.WechatCustRelationService;
import com.howbuy.message.SimpleMessage;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 更新所有高端客户微信信息  和   企业微信客户投顾关联关系
 * @date 2025年7月23日 09:59:25
 * @since JDK 1.8
 */
@Slf4j
@Component
public class SyncCustHboneRelationJob extends AbstractBatchMessageJob {

    /**
     * 调度消息队列，nacos中TOPIC名称： TOPIC_SYNC_CUST_HBONE_RELATION_JOB
     */
    @Value("${sync_cust_hbone_relation_channel}")
    private String queue;

    @Autowired
    private WechatCustRelationService wechatCustRelationService;

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        log.info("SyncCustHboneRelationJob process start");
        try {
            BaseCompanyNoRequest request = JSON.parseObject((String) message.getContent(), BaseCompanyNoRequest.class);
            String companyNoEnum = null;
            //使用 调度参数中指定的 companyNo
            if(request!=null && StringUtil.isNotNullStr(request.getCompanyNo())){
                //该同步 目前只支持  好买财富
                companyNoEnum = request.getCompanyNo();
            }
            if(companyNoEnum==null){
                //该同步 目前只支持  好买财富
                companyNoEnum=Constants.DEFAULT_COMPANY_NO;
            }
            wechatCustRelationService.updateAllCustInfoList(companyNoEnum);
        } catch (Exception e) {
            log.error("error in SyncCustHboneRelationJob", e);
        }
        log.info("SyncCustHboneRelationJob process end");
    }

    @Override
    protected String getQuartMessageChannel() {
        // 返回调度配置的队列名
        return queue;
    }

}